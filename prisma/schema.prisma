// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../prisma/generated"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model UserIncident {
  id                              Int   @id @default(autoincrement())
  bookingRefrenceNumber           String
  leadGuestName                   String
  reasonForBan                    String
  isDiscoveredAfterDeparture      Boolean 
  isPoliceInvolved                Boolean?
  crimeReferenceNumber            String?
  otherInformation                String?
  resolvedAt                      DateTime?
  status                          UserIncidentStatus   @default(PENDING)
  attachments                     IncidenAttachment[] @relation("UserIncidentToAttachment")
  
  @@map("userIncidents")
}

model UserIncidentAudit {
  id                Int   @id @default(autoincrement())
  userIncidentId    Int
  createdBy         String
  updateBy          String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime? @updatedAt

  @@index([userIncidentId])
  @@map("userIncidentAudit")
}

model IncidenAttachment {
  id                 Int   @id @default(autoincrement())
  userIncidentId     Int
  fileName          String
  fileUrl           String

  @@index([userIncidentId])
  @@map("IncidenAttachment")

  userIncident    UserIncident @relation("UserIncidentToAttachment", fields: [userIncidentId], references: [id])
}

model IncidenAttachmentAudit {
  id                Int   @id @default(autoincrement())
  incidenAttachmentId Int
  createdBy         String
  updateBy          String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime? @updatedAt

  @@index([incidenAttachmentId])
  @@map("IncidenAttachmentAudit")
}

enum UserIncidentStatus {
  DRAFT
  PENDING
  IN_PROGRESS
  CLOSED
}

model CrosscoreRequest {
  id               Int   @id @default(autoincrement())
  userId           String
  requestId        String   @unique
  verificationType String
  status           String   @default("pending")
  requestData      Json
  responseData     Json?
  experianRequestId String?
  experianStatus   String?
  errorMessage     String?
  webhookReceived  Boolean  @default(false)
  webhookData      Json?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
  completedAt      DateTime?

  @@index([userId, status])
  @@index([createdAt])
  @@index([status, verificationType])
  @@index([experianRequestId])
  @@map("crosscore_requests")
}
