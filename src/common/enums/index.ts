// User Incident Status Enum
export enum UserIncidentStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  RESOLVED = 'resolved',
}

// User Incident Priority Enum
export enum UserIncidentPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// User Incident Type Enum
export enum UserIncidentType {
  IDENTITY_VERIFICATION = 'identity_verification',
  DOCUMENT_VERIFICATION = 'document_verification',
  ADDRESS_VERIFICATION = 'address_verification',
  FRAUD_DETECTION = 'fraud_detection',
  COMPLIANCE_CHECK = 'compliance_check',
  OTHER = 'other',
}

// CrossCore Request Status Enum
export enum CrosscoreStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

// CrossCore Verification Type Enum
export enum CrosscoreVerificationType {
  IDENTITY_VERIFICATION = 'identity_verification',
  DOCUMENT_VERIFICATION = 'document_verification',
  ADDRESS_VERIFICATION = 'address_verification',
  CREDIT_CHECK = 'credit_check',
  FRAUD_CHECK = 'fraud_check',
  KYC_VERIFICATION = 'kyc_verification',
}

// HTTP Status Code Enum
export enum HttpStatusCode {
  OK = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  UNPROCESSABLE_ENTITY = 422,
  INTERNAL_SERVER_ERROR = 500,
  BAD_GATEWAY = 502,
  SERVICE_UNAVAILABLE = 503,
}

// Environment Enum
export enum Environment {
  DEVELOPMENT = 'development',
  STAGING = 'staging',
  PRODUCTION = 'production',
  TEST = 'test',
}

// Log Level Enum
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
  TRACE = 'trace',
}

// Sort Order Enum
export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc',
}

// Database Operation Enum
export enum DatabaseOperation {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
}

// API Response Status Enum
export enum ApiResponseStatus {
  SUCCESS = 'success',
  ERROR = 'error',
  WARNING = 'warning',
}

// Webhook Event Type Enum
export enum WebhookEventType {
  CROSSCORE_UPDATE = 'crosscore_update',
  VERIFICATION_COMPLETE = 'verification_complete',
  VERIFICATION_FAILED = 'verification_failed',
  INCIDENT_CREATED = 'incident_created',
  INCIDENT_UPDATED = 'incident_updated',
}

// Content Type Enum
export enum ContentType {
  JSON = 'application/json',
  XML = 'application/xml',
  FORM_URLENCODED = 'application/x-www-form-urlencoded',
  MULTIPART_FORM_DATA = 'multipart/form-data',
  TEXT_PLAIN = 'text/plain',
}

// Cache Duration Enum (in seconds)
export enum CacheDuration {
  SHORT = 300, // 5 minutes
  MEDIUM = 1800, // 30 minutes
  LONG = 3600, // 1 hour
  VERY_LONG = 86400, // 24 hours
}

// Rate Limit Window Enum (in seconds)
export enum RateLimitWindow {
  MINUTE = 60,
  HOUR = 3600,
  DAY = 86400,
}

// Experian Status Mapping Enum
export enum ExperianStatus {
  SUBMITTED = 'submitted',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  ERROR = 'error',
  CANCELLED = 'cancelled',
}
