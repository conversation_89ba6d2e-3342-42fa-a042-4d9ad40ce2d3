import { UserIncidentRepository } from './userIncident.repository';
import { UserIncidentMapper } from './userIncident.mapper';
import { getErrorMessage } from '../common/utils';
import {
  CreateUserIncidentRequest,
  UpdateUserIncidentRequest,
  GetUserIncidentsQuery,
  UserIncidentResponse,
  PaginatedUserIncidentsResponse,
} from './userIncident.dto';
import { UserIncidentStatus } from '../../prisma/generated';

export class UserIncidentService {
  private repository: UserIncidentRepository;

  constructor() {
    this.repository = new UserIncidentRepository();
  }

  /**
   * Creates a new user incident
   */
  async createIncident(request: CreateUserIncidentRequest): Promise<UserIncidentResponse> {
    try {
      const entityData = UserIncidentMapper.toEntity(request);
      entityData.status = UserIncidentStatus.DRAFT;
      const incident = await this.repository.create(entityData, request.createdBy);

      // Get audit information
      const auditInfo = await this.repository.getAuditInfo(incident.id);

      return UserIncidentMapper.toResponse(
        incident,
        auditInfo
          ? {
              createdBy: auditInfo.createdBy,
              updatedBy: auditInfo.updateBy || undefined,
              createdAt: auditInfo.createdAt,
              updatedAt: auditInfo.updatedAt || undefined,
            }
          : undefined
      );
    } catch (error) {
      throw new Error(`Failed to create incident: ${getErrorMessage(error)}`);
    }
  }

  /**
   * Gets a user incident by ID
   */
  async getIncidentById(id: number): Promise<UserIncidentResponse | null> {
    try {
      const incident = await this.repository.findById(id);
      if (!incident) {
        return null;
      }

      // Get audit information
      const auditInfo = await this.repository.getAuditInfo(incident.id);

      return UserIncidentMapper.toResponse(
        incident,
        auditInfo
          ? {
              createdBy: auditInfo.createdBy,
              updatedBy: auditInfo.updateBy || undefined,
              createdAt: auditInfo.createdAt,
              updatedAt: auditInfo.updatedAt || undefined,
            }
          : undefined
      );
    } catch (error) {
      throw new Error(`Failed to get incident: ${getErrorMessage(error)}`);
    }
  }

  /**
   * Gets user incidents with filtering and pagination
   */
  async getIncidents(query: GetUserIncidentsQuery): Promise<PaginatedUserIncidentsResponse> {
    try {
      const { incidents, total } = await this.repository.findMany(query);
      const page = query.page || 1;
      const limit = query.limit || 10;

      // Get audit information for all incidents
      const incidentIds = incidents.map(incident => incident.id);
      const auditRecords = await this.repository.getAuditInfoBatch(incidentIds);

      // Create audit map
      const auditMap = new Map(
        auditRecords.map(audit => [
          audit.userIncidentId,
          {
            createdBy: audit.createdBy,
            updatedBy: audit.updateBy || undefined,
            createdAt: audit.createdAt,
            updatedAt: audit.updatedAt || undefined,
          },
        ])
      );

      return UserIncidentMapper.toPaginatedResponse(incidents, page, limit, total, auditMap);
    } catch (error) {
      throw new Error(`Failed to get incidents: ${getErrorMessage(error)}`);
    }
  }

  /**
   * Gets all incidents for a specific user
   */
  async getIncidentsByUserId(userId: string): Promise<UserIncidentResponse[]> {
    try {
      const incidents = await this.repository.findByUserId(userId);

      // Get audit information for all incidents
      const incidentIds = incidents.map(incident => incident.id);
      const auditRecords = await this.repository.getAuditInfoBatch(incidentIds);

      // Create audit map
      const auditMap = new Map(
        auditRecords.map(audit => [
          audit.userIncidentId,
          {
            createdBy: audit.createdBy,
            updatedBy: audit.updateBy || undefined,
            createdAt: audit.createdAt,
            updatedAt: audit.updatedAt || undefined,
          },
        ])
      );

      return UserIncidentMapper.toResponseArray(incidents, auditMap);
    } catch (error) {
      throw new Error(`Failed to get user incidents: ${getErrorMessage(error)}`);
    }
  }

  /**
   * Updates a user incident
   */
  async updateIncident(
    id: number,
    request: UpdateUserIncidentRequest
  ): Promise<UserIncidentResponse | null> {
    try {
      const updateData = UserIncidentMapper.toUpdateEntity(request);
      const incident = await this.repository.updateById(id, updateData, request.updatedBy);
      if (!incident) {
        return null;
      }

      // Get audit information
      const auditInfo = await this.repository.getAuditInfo(incident.id);

      return UserIncidentMapper.toResponse(
        incident,
        auditInfo
          ? {
              createdBy: auditInfo.createdBy,
              updatedBy: auditInfo.updateBy || undefined,
              createdAt: auditInfo.createdAt,
              updatedAt: auditInfo.updatedAt || undefined,
            }
          : undefined
      );
    } catch (error) {
      throw new Error(`Failed to update incident: ${getErrorMessage(error)}`);
    }
  }

  /**
   * Deletes a user incident
   */
  async deleteIncident(id: number): Promise<boolean> {
    try {
      const incident = await this.repository.deleteById(id);
      return incident !== null;
    } catch (error) {
      throw new Error(`Failed to delete incident: ${getErrorMessage(error)}`);
    }
  }

  /**
   * Updates incident status to IN_PROGRESS
   */
  async assignIncident(id: number, updatedBy: string): Promise<UserIncidentResponse | null> {
    try {
      const updateData = {
        status: 'IN_PROGRESS' as const,
        update_by: updatedBy,
      };
      const incident = await this.repository.updateById(id, updateData);
      if (!incident) {
        return null;
      }
      return UserIncidentMapper.toResponse(incident);
    } catch (error) {
      throw new Error(`Failed to assign incident: ${getErrorMessage(error)}`);
    }
  }

  /**
   * Resolves an incident (sets status to CLOSED)
   */
  async resolveIncident(id: number, updatedBy: string): Promise<UserIncidentResponse | null> {
    try {
      const updateData = {
        status: 'CLOSED' as const,
        resolved_at: new Date(),
        update_by: updatedBy,
      };
      const incident = await this.repository.updateById(id, updateData);
      if (!incident) {
        return null;
      }
      return UserIncidentMapper.toResponse(incident);
    } catch (error) {
      throw new Error(`Failed to resolve incident: ${getErrorMessage(error)}`);
    }
  }

  /**
   * Gets incident statistics
   */
  async getStatistics(): Promise<{
    total: number;
    byStatus: Record<string, number>;
  }> {
    try {
      return await this.repository.getStatistics();
    } catch (error) {
      throw new Error(`Failed to get statistics: ${getErrorMessage(error)}`);
    }
  }
}
