import { Server } from '@hapi/hapi';
import { UserIncidentController } from './userIncident.controller';
import { API_CONSTANTS } from '../common/constants';

export async function registerUserIncidentRoutes(server: Server): Promise<void> {
  // Initialize controller
  const userIncidentController = new UserIncidentController();

  // User Incident routes
  server.route([
    {
      method: 'POST',
      path: `${API_CONSTANTS.BASE_PATH}/${API_CONSTANTS.VERSION}/customer-incidents`,
      handler: userIncidentController.createIncident.bind(userIncidentController),
      options: {
        description: 'Create a new user incident',
        tags: ['api', 'customer-incidents'],
      },
    },
    {
      method: 'GET',
      path: `${API_CONSTANTS.BASE_PATH}/${API_CONSTANTS.VERSION}/customer-incidents/{id}`,
      handler: userIncidentController.getIncidentById.bind(userIncidentController),
      options: {
        description: 'Get incident by ID',
        tags: ['api', 'customer-incidents'],
      },
    },
    {
      method: 'GET',
      path: `${API_CONSTANTS.BASE_PATH}/${API_CONSTANTS.VERSION}/customer-incidents`,
      handler: userIncidentController.getIncidents.bind(userIncidentController),
      options: {
        description: 'Get incidents with filtering and pagination',
        tags: ['api', 'customer-incidents'],
      },
    },
    {
      method: 'GET',
      path: `${API_CONSTANTS.BASE_PATH}/${API_CONSTANTS.VERSION}/users/{userId}/incidents`,
      handler: userIncidentController.getIncidentsByUserId.bind(userIncidentController),
      options: {
        description: 'Get incidents for a specific user',
        tags: ['api', 'customer-incidents', 'users'],
      },
    },
    {
      method: 'PUT',
      path: `${API_CONSTANTS.BASE_PATH}/${API_CONSTANTS.VERSION}/customer-incidents/{id}`,
      handler: userIncidentController.updateIncident.bind(userIncidentController),
      options: {
        description: 'Update an incident',
        tags: ['api', 'customer-incidents'],
      },
    },
    {
      method: 'DELETE',
      path: `${API_CONSTANTS.BASE_PATH}/${API_CONSTANTS.VERSION}/customer-incidents/{id}`,
      handler: userIncidentController.deleteIncident.bind(userIncidentController),
      options: {
        description: 'Delete an incident',
        tags: ['api', 'customer-incidents'],
      },
    },
    {
      method: 'POST',
      path: `${API_CONSTANTS.BASE_PATH}/${API_CONSTANTS.VERSION}/customer-incidents/{id}/assign`,
      handler: userIncidentController.assignIncident.bind(userIncidentController),
      options: {
        description: 'Assign an incident to a user',
        tags: ['api', 'customer-incidents'],
      },
    },
    {
      method: 'POST',
      path: `${API_CONSTANTS.BASE_PATH}/${API_CONSTANTS.VERSION}/customer-incidents/{id}/resolve`,
      handler: userIncidentController.resolveIncident.bind(userIncidentController),
      options: {
        description: 'Resolve an incident',
        tags: ['api', 'customer-incidents'],
      },
    },
    {
      method: 'GET',
      path: `${API_CONSTANTS.BASE_PATH}/${API_CONSTANTS.VERSION}/customer-incidents/statistics`,
      handler: userIncidentController.getStatistics.bind(userIncidentController),
      options: {
        description: 'Get incident statistics',
        tags: ['api', 'customer-incidents', 'statistics'],
      },
    },
  ]);

  console.log('User Incident routes registered');
}
