import { PrismaClient, UserIncident, UserIncidentAudit, Prisma } from '../../prisma/generated';
import { GetUserIncidentsQuery } from './userIncident.dto';

export class UserIncidentRepository {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  /**
   * Creates a new user incident with audit record
   */
  async create(
    incidentData: Prisma.UserIncidentCreateInput,
    createdBy: string
  ): Promise<UserIncident> {
    return await this.prisma.$transaction(async tx => {
      // Create the incident
      const incident = await tx.userIncident.create({
        data: incidentData,
      });

      // Create the audit record
      await tx.userIncidentAudit.create({
        data: {
          userIncidentId: incident.id,
          createdBy: createdBy,
          createdAt: new Date(),
        },
      });

      return incident;
    });
  }

  /**
   * Finds a user incident by ID
   */
  async findById(id: number): Promise<UserIncident | null> {
    return await this.prisma.userIncident.findUnique({
      where: { id },
    });
  }

  /**
   * Finds user incidents with filtering, pagination, and sorting
   */
  async findMany(
    query: GetUserIncidentsQuery
  ): Promise<{ incidents: UserIncident[]; total: number }> {
    const where: Prisma.UserIncidentWhereInput = {};

    // Build filter object
    if (query.status) {
      where.status = query.status;
    }
    // Note: createdBy filter will be handled separately since it's now in audit table

    // Build sort object - use id as default since created_at is now in audit table
    const sortField = query.sortBy || 'id';
    const sortOrder = query.sortOrder === 'asc' ? 'asc' : 'desc';
    const orderBy: Prisma.UserIncidentOrderByWithRelationInput = { [sortField]: sortOrder };

    // Pagination
    const page = query.page || 1;
    const limit = query.limit || 10;
    const skip = (page - 1) * limit;

    // If filtering by createdBy, we need to use a more complex query
    if (query.createdBy) {
      // Get incident IDs that match the createdBy filter from audit table
      const auditRecords = await this.prisma.userIncidentAudit.findMany({
        where: { createdBy: query.createdBy },
        select: { userIncidentId: true },
      });
      const incidentIds = auditRecords.map(audit => audit.userIncidentId);

      if (incidentIds.length === 0) {
        return { incidents: [], total: 0 };
      }

      where.id = { in: incidentIds };
    }

    // Execute queries
    const [incidents, total] = await Promise.all([
      this.prisma.userIncident.findMany({
        where,
        orderBy,
        skip,
        take: limit,
      }),
      this.prisma.userIncident.count({ where }),
    ]);

    return { incidents, total };
  }

  /**
   * Finds user incidents by created by user (using audit table)
   */
  async findByUserId(userId: string): Promise<UserIncident[]> {
    // Get incident IDs from audit table
    const auditRecords = await this.prisma.userIncidentAudit.findMany({
      where: { createdBy: userId },
      select: { userIncidentId: true },
    });
    const incidentIds = auditRecords.map(audit => audit.userIncidentId);

    if (incidentIds.length === 0) {
      return [];
    }

    return await this.prisma.userIncident.findMany({
      where: { id: { in: incidentIds } },
      orderBy: { id: 'desc' },
    });
  }

  /**
   * Updates a user incident by ID with audit record
   */
  async updateById(
    id: number,
    updateData: Prisma.UserIncidentUpdateInput,
    updatedBy?: string
  ): Promise<UserIncident | null> {
    try {
      return await this.prisma.$transaction(async tx => {
        // Update the incident
        const incident = await tx.userIncident.update({
          where: { id },
          data: updateData,
        });

        // Update the audit record if updatedBy is provided
        if (updatedBy) {
          await tx.userIncidentAudit.updateMany({
            where: { userIncidentId: id },
            data: {
              updateBy: updatedBy,
              updatedAt: new Date(),
            },
          });
        }

        return incident;
      });
    } catch (error) {
      return null; // Record not found or other error
    }
  }

  /**
   * Deletes a user incident by ID
   */
  async deleteById(id: number): Promise<UserIncident | null> {
    try {
      return await this.prisma.userIncident.delete({
        where: { id },
      });
    } catch (error) {
      return null; // Record not found or other error
    }
  }

  /**
   * Finds incidents by status
   */
  async findByStatus(
    status: 'DRAFT' | 'PENDING' | 'IN_PROGRESS' | 'CLOSED'
  ): Promise<UserIncident[]> {
    return await this.prisma.userIncident.findMany({
      where: { status },
      orderBy: { id: 'desc' },
    });
  }

  /**
   * Gets audit information for a user incident
   */
  async getAuditInfo(userIncidentId: number): Promise<UserIncidentAudit | null> {
    return await this.prisma.userIncidentAudit.findFirst({
      where: { userIncidentId },
    });
  }

  /**
   * Gets audit information for multiple user incidents
   */
  async getAuditInfoBatch(userIncidentIds: number[]): Promise<UserIncidentAudit[]> {
    return await this.prisma.userIncidentAudit.findMany({
      where: { userIncidentId: { in: userIncidentIds } },
    });
  }

  /**
   * Gets incident statistics
   */
  async getStatistics(): Promise<{
    total: number;
    byStatus: Record<string, number>;
  }> {
    const [total, statusStats] = await Promise.all([
      this.prisma.userIncident.count(),
      this.prisma.userIncident.groupBy({
        by: ['status'],
        _count: {
          status: true,
        },
      }),
    ]);

    const byStatus: Record<string, number> = {};
    statusStats.forEach(stat => {
      byStatus[stat.status] = stat._count.status;
    });

    return { total, byStatus };
  }
}
