import Joi from '@hapi/joi';

// Request DTOs
export interface CreateUserIncidentRequest {
  bookingReferenceNumber: string;
  leadGuestName: string;
  reasonForBan: string;
  isDiscoveredAfterDeparture: boolean;
  isPoliceInvolved?: boolean;
  crimeReferenceNumber?: string;
  otherInformation?: string;
  createdBy: string;
}

export interface UpdateUserIncidentRequest {
  bookingReferenceNumber?: string;
  leadGuestName?: string;
  reasonForBan?: string;
  isDiscoveredAfterDeparture?: boolean;
  isPoliceInvolved?: boolean;
  crimeReferenceNumber?: string;
  otherInformation?: string;
  status?: 'DRAFT' | 'PENDING' | 'IN_PROGRESS' | 'CLOSED';
  updatedBy?: string;
}

export interface GetUserIncidentsQuery {
  status?: 'DRAFT' | 'PENDING' | 'IN_PROGRESS' | 'CLOSED';
  createdBy?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Response DTOs
export interface UserIncidentResponse {
  id: number;
  bookingReferenceNumber: string;
  leadGuestName: string;
  reasonForBan: string;
  isDiscoveredAfterDeparture: boolean;
  isPoliceInvolved?: boolean;
  crimeReferenceNumber?: string;
  otherInformation?: string;
  createdBy: string;
  updatedBy?: string;
  createdAt: string;
  updatedAt?: string;
  resolvedAt?: string;
  status: 'DRAFT' | 'PENDING' | 'IN_PROGRESS' | 'CLOSED';
}

export interface PaginatedUserIncidentsResponse {
  incidents: UserIncidentResponse[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Validation schemas
export const createUserIncidentSchema = Joi.object({
  bookingReferenceNumber: Joi.string().required().min(1).max(100),
  leadGuestName: Joi.string().required().min(1).max(200),
  reasonForBan: Joi.string().required().min(1).max(1000),
  isDiscoveredAfterDeparture: Joi.boolean().required(),
  isPoliceInvolved: Joi.boolean().optional(),
  crimeReferenceNumber: Joi.string().optional().max(100),
  otherInformation: Joi.string().optional().max(2000),
});

export const updateUserIncidentSchema = Joi.object({
  bookingReferenceNumber: Joi.string().optional().min(1).max(100),
  leadGuestName: Joi.string().optional().min(1).max(200),
  reasonForBan: Joi.string().optional().min(1).max(1000),
  isDiscoveredAfterDeparture: Joi.boolean().optional(),
  isPoliceInvolved: Joi.boolean().optional(),
  crimeReferenceNumber: Joi.string().optional().max(100),
  otherInformation: Joi.string().optional().max(2000),
  status: Joi.string().optional().valid('DRAFT', 'PENDING', 'IN_PROGRESS', 'CLOSED'),
  updatedBy: Joi.string().optional().min(1).max(100),
});

export const getUserIncidentsQuerySchema = Joi.object({
  status: Joi.string().optional().valid('DRAFT', 'PENDING', 'IN_PROGRESS', 'CLOSED'),
  createdBy: Joi.string().optional().min(1).max(100),
  page: Joi.number().optional().min(1).default(1),
  limit: Joi.number().optional().min(1).max(100).default(10),
  sortBy: Joi.string().optional().valid('id', 'status').default('id'),
  sortOrder: Joi.string().optional().valid('asc', 'desc').default('desc'),
});
