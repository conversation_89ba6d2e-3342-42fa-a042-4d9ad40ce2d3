import { UserIncident, Prisma } from '../../prisma/generated';
import {
  CreateUserIncidentRequest,
  UpdateUserIncidentRequest,
  UserIncidentResponse,
  PaginatedUserIncidentsResponse,
} from './userIncident.dto';

export class UserIncidentMapper {
  /**
   * Maps a CreateUserIncidentRequest DTO to a UserIncident entity
   */
  static toEntity(dto: CreateUserIncidentRequest): Prisma.UserIncidentCreateInput {
    return {
      bookingRefrenceNumber: dto.bookingReferenceNumber,
      leadGuestName: dto.leadGuestName,
      reasonForBan: dto.reasonForBan,
      isDiscoveredAfterDeparture: dto.isDiscoveredAfterDeparture,
      isPoliceInvolved: dto.isPoliceInvolved,
      crimeReferenceNumber: dto.crimeReferenceNumber,
      otherInformation: dto.otherInformation,
      status: 'DRAFT',
    };
  }

  /**
   * Maps an UpdateUserIncidentRequest DTO to a UserIncident entity update
   */
  static toUpdateEntity(dto: UpdateUserIncidentRequest): Prisma.UserIncidentUpdateInput {
    const updateData: Prisma.UserIncidentUpdateInput = {};

    if (dto.bookingReferenceNumber !== undefined) {
      updateData.bookingRefrenceNumber = dto.bookingReferenceNumber;
    }
    if (dto.leadGuestName !== undefined) {
      updateData.leadGuestName = dto.leadGuestName;
    }
    if (dto.reasonForBan !== undefined) {
      updateData.reasonForBan = dto.reasonForBan;
    }
    if (dto.isDiscoveredAfterDeparture !== undefined) {
      updateData.isDiscoveredAfterDeparture = dto.isDiscoveredAfterDeparture;
    }
    if (dto.isPoliceInvolved !== undefined) {
      updateData.isPoliceInvolved = dto.isPoliceInvolved;
    }
    if (dto.crimeReferenceNumber !== undefined) {
      updateData.crimeReferenceNumber = dto.crimeReferenceNumber;
    }
    if (dto.otherInformation !== undefined) {
      updateData.otherInformation = dto.otherInformation;
    }
    if (dto.status !== undefined) {
      updateData.status = dto.status;
      // Set resolvedAt when status changes to CLOSED
      if (dto.status === 'CLOSED') {
        updateData.resolvedAt = new Date();
      }
    }

    return updateData;
  }

  /**
   * Maps a UserIncident entity to a UserIncidentResponse DTO
   * Note: Audit fields (createdBy, updatedBy, createdAt, updatedAt) are now in UserIncidentAudit table
   */
  static toResponse(
    entity: UserIncident,
    audit?: { createdBy: string; updatedBy?: string; createdAt: Date; updatedAt?: Date }
  ): UserIncidentResponse {
    return {
      id: entity.id,
      bookingReferenceNumber: entity.bookingRefrenceNumber,
      leadGuestName: entity.leadGuestName,
      reasonForBan: entity.reasonForBan,
      isDiscoveredAfterDeparture: entity.isDiscoveredAfterDeparture,
      isPoliceInvolved: entity.isPoliceInvolved || undefined,
      crimeReferenceNumber: entity.crimeReferenceNumber || undefined,
      otherInformation: entity.otherInformation || undefined,
      createdBy: audit?.createdBy || '',
      updatedBy: audit?.updatedBy || undefined,
      status: entity.status,
      createdAt: audit?.createdAt?.toISOString() || new Date().toISOString(),
      updatedAt: audit?.updatedAt?.toISOString(),
      resolvedAt: entity.resolvedAt?.toISOString(),
    };
  }

  /**
   * Maps an array of UserIncident entities to UserIncidentResponse DTOs
   */
  static toResponseArray(
    entities: UserIncident[],
    auditMap?: Map<
      number,
      { createdBy: string; updatedBy?: string; createdAt: Date; updatedAt?: Date }
    >
  ): UserIncidentResponse[] {
    return entities.map(entity => {
      const audit = auditMap?.get(entity.id);
      return this.toResponse(entity, audit);
    });
  }

  /**
   * Maps UserIncident entities with pagination info to PaginatedUserIncidentsResponse
   */
  static toPaginatedResponse(
    entities: UserIncident[],
    page: number,
    limit: number,
    total: number,
    auditMap?: Map<
      number,
      { createdBy: string; updatedBy?: string; createdAt: Date; updatedAt?: Date }
    >
  ): PaginatedUserIncidentsResponse {
    return {
      incidents: this.toResponseArray(entities, auditMap),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }
}
